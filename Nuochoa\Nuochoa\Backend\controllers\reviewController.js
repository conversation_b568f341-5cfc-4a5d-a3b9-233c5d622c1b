// controllers/reviewController.js
import Review from '../models/Review.js';
import Order from '../models/Order.js';


// Function để thêm đánh giá
const addReview = async (req, res) => {
  try {
    const { productId, orderId, rating, comment } = req.body;
    const userId = req.body?.userId || req.user?.userId || req.user?.id || req.user?.uid;
    
    if (!userId) {
      return res.json({ success: false, message: "Không tìm thấy thông tin user" });
    }
    
    console.log("⭐ Adding review:", { productId, orderId, rating, userId });
    
    // Kiểm tra xem đơn hàng có tồn tại và thuộc về user không
    const order = await Order.findOne({ _id: orderId, userId });
    if (!order) {
      return res.json({ success: false, message: "<PERSON>hông tìm thấy đơn hàng" });
    }
    
    // Ki<PERSON>m tra xem đơn hàng đã được giao chưa
    if (order.status.toLowerCase() !== 'delivered') {
      return res.json({
        success: false,
        message: "Chỉ có thể đánh giá sản phẩm sau khi đã nhận được hàng"
      });
    }
    
    // Kiểm tra xem sản phẩm có trong đơn hàng không
    const productInOrder = order.items.find(item => item._id === productId);
    if (!productInOrder) {
      return res.json({ 
        success: false, 
        message: "Sản phẩm không có trong đơn hàng này" 
      });
    }
    
    // Kiểm tra xem đã đánh giá sản phẩm này chưa
    const existingReview = await Review.findOne({ 
      userId, 
      productId, 
      orderId 
    });
    
    if (existingReview) {
      return res.json({ 
        success: false, 
        message: "Bạn đã đánh giá sản phẩm này rồi" 
      });
    }
    
    // Tạo đánh giá mới
    const newReview = new Review({
      userId,
      productId,
      orderId,
      rating: Math.max(1, Math.min(5, rating)), // Đảm bảo rating từ 1-5
      comment: comment || '',
      date: new Date()
    });
    
    await newReview.save();
    
    console.log("✅ Review added successfully:", newReview._id);
    
    res.json({
      success: true,
      message: "Đánh giá đã được gửi thành công",
      review: newReview
    });
    
  } catch (error) {
    console.error("❌ Error adding review:", error);
    res.json({
      success: false,
      message: "Lỗi khi thêm đánh giá: " + error.message
    });
  }
};

// Function để lấy đánh giá của sản phẩm
const getProductReviews = async (req, res) => {
  try {
    const { productId } = req.params;

    console.log("📋 Getting reviews for product:", productId);

    const reviews = await Review.find({ productId }).sort({ date: -1 });

    // Thêm thông tin user cho mỗi review (giấu thông tin nhạy cảm)
    const reviewsWithUserInfo = reviews.map(review => ({
      ...review.toObject(),
      userDisplayName: `Khách hàng ${review.userId.slice(-4)}`,
      isVerifiedPurchase: true // Vì chỉ có thể review sau khi mua
    }));

    // Tính toán rating trung bình
    const totalRating = reviews.reduce((sum, review) => sum + review.rating, 0);
    const averageRating = reviews.length > 0 ? (totalRating / reviews.length).toFixed(1) : 0;

    console.log(`✅ Found ${reviews.length} reviews, average rating: ${averageRating}`);

    res.json({
      success: true,
      reviews: reviewsWithUserInfo,
      averageRating: parseFloat(averageRating),
      totalReviews: reviews.length
    });

  } catch (error) {
    console.error("❌ Error getting product reviews:", error);
    res.json({
      success: false,
      message: "Lỗi khi lấy đánh giá sản phẩm: " + error.message
    });
  }
};

// Function để lấy đánh giá của user
const getUserReviews = async (req, res) => {
  try {
    const userId = req.body?.userId || req.user?.userId || req.user?.id || req.user?.uid;
    
    if (!userId) {
      return res.json({ success: false, message: "Không tìm thấy thông tin user" });
    }
    
    console.log("📋 Getting reviews for user:", userId);
    
    const reviews = await Review.find({ userId }).sort({ date: -1 });
    
    console.log(`✅ Found ${reviews.length} reviews for user`);
    
    res.json({
      success: true,
      reviews: reviews
    });
    
  } catch (error) {
    console.error("❌ Error getting user reviews:", error);
    res.json({
      success: false,
      message: "Lỗi khi lấy đánh giá của user: " + error.message
    });
  }
};

const getAllReviews = async (req, res) => {
  try {
    console.log("📋 Getting all reviews");

    // Lấy tất cả reviews từ database và populate các trường liên quan
    const reviews = await Review.find()
      .select('-__v')  // Loại bỏ trường __v
      .sort({ date: -1 })
      .populate('userId', 'name email')  // Populate tên và email người dùng từ bảng User
      .populate('productId', 'name price category brand')  // Populate thông tin sản phẩm từ bảng Product
      .populate('orderId', 'orderCode status amount date');  // Populate thông tin đơn hàng từ bảng Order

    console.log("📋 Found reviews:", reviews.length);

    // Trả về kết quả
    res.json({
      success: true,
      reviews: reviews,
      totalReviews: reviews.length
    });

  } catch (error) {
    console.error("❌ Error getting all reviews:", error);
    res.json({
      success: false,
      message: "Lỗi khi lấy tất cả đánh giá: " + error.message
    });
  }
};

// Function để lấy tất cả user đã đánh giá
const getAllUsersWhoReviewed = async (req, res) => {
  try {
    console.log("📋 Getting all users who have reviewed products");

    // Sử dụng distinct để lấy tất cả userId đã đánh giá
    const userIds = await Review.distinct('userId');

    console.log(`✅ Found ${userIds.length} users who have reviewed`);

    // Trả về danh sách các userId
    res.json({
      success: true,
      userIds: userIds,
      totalUsers: userIds.length
    });

  } catch (error) {
    console.error("❌ Error getting all users who have reviewed:", error);
    res.json({
      success: false,
      message: "Lỗi khi lấy người dùng đã đánh giá: " + error.message
    });
  }
};

export { getAllUsersWhoReviewed };

export { addReview, getProductReviews, getUserReviews, getAllReviews };



